<?php

namespace App\Modules\RequestDelete\Jobs;

use App\Modules\CustomLogger\Services\AuthLogger;
use App\Modules\CustomLogger\Services\UserLoggerTrait;
use App\Modules\RequestDelete\Services\DomainRejectRequestJobService;
use App\Util\Constant\QueueConnection;
use App\Util\Constant\QueueTypes;
use App\Util\Helper\DomainParser;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class DomainRejectRequestJob implements ShouldBeUnique, ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, UserLoggerTrait;

    private $params;

    /**
     * if process takes longer than indicated timeout ie. --timeout=30
     * set the job to failed job
     */
    public $failOnTimeout = true;

    /**
     * Create a new job instance.
     */
    public function __construct(
        string $domainId,
        string $domainName,
        string $userId,
        string $userEmail,
        string $reason,
        string $createdDate,
        string $supportNote = null,
        int $adminId = null,
        string $adminName = null,
        string $adminEmail = null
    ) {
        $registry = DomainParser::getRegistryName($domainName);

        $this->params = [
            'domainId' => $domainId,
            'domainName' => $domainName,
            'userId' => $userId,
            'userEmail' => $userEmail,
            'reason' => $reason,
            'createdDate' => $createdDate,
            'supportNote' => $supportNote,
            'adminId' => $adminId,
            'adminName' => $adminName,
            'adminEmail' => $adminEmail
        ];

        $this->onConnection(QueueConnection::DOMAIN_REJECT_REQUEST);
        $this->onQueue(QueueTypes::DOMAIN_REJECT_REQUEST[$registry]);
    }

    public $uniqueFor = 3600;

    public function uniqueId(): int
    {
        return Carbon::now()->timestamp . $this->params['domainId'];
    }

    public function handle(): void
    {
        try {
            app(AuthLogger::class)->info("Starting domain reject request job for domain: {$this->params['domainName']} (ID: {$this->params['domainId']})");
            DomainRejectRequestJobService::instance()->reactivateDomain($this->params);
            app(AuthLogger::class)->info("Successfully completed domain reject request job for domain: {$this->params['domainName']}");
        } catch (Exception $e) {
            app(AuthLogger::class)->error("Domain reject request job failed for domain: {$this->params['domainName']} (ID: {$this->params['domainId']}). Error: " . $e->getMessage());
            throw $e;
        }
    }
}
