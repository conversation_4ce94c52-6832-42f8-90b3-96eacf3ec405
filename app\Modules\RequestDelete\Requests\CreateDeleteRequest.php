<?php

namespace App\Modules\RequestDelete\Requests;

// use App\Modules\PendingDelete\Constants\StatusTypes;

use App\Modules\RequestDelete\Constants\StatusTypes;
use App\Modules\RequestDelete\Services\DatabaseQueryService;
use App\Modules\RequestDelete\Services\DomainDeleteService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreateDeleteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    { 
        return [
            'reason' => ['string', 'min:10'],
        ];
    }

    public function create_deleteRequest() {
        $result = DomainDeleteService::instance()->createDeleteRequest($this);

        return redirect()->back()->with('successMessage', $result['message']);
    }
}
