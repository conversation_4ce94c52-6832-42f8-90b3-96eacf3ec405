[2025-09-04 06:37:50] local.INFO: Successfully removed clientDeleteProhibited status for domain: ahalawsaw.net  
[2025-09-04 06:37:52] local.INFO: Successfully deleted domain from EPP and datastore: ahalawsaw.net  
[2025-09-04 06:38:18] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-04 06:38:23] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-04 06:38:33] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-04 06:41:48] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-04 06:41:56] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation \"redemption_orders\" does not exist
LINE 1: ...users\".\"id\" = \"user_contacts\".\"user_id\" left join \"redemptio...
                                                             ^ (Connection: client, SQL: select count(*) as aggregate from \"pending_domain_deletions\" inner join \"registered_domains\" on \"registered_domains\".\"id\" = \"pending_domain_deletions\".\"registered_domain_id\" inner join \"domains\" on \"domains\".\"id\" = \"registered_domains\".\"domain_id\" inner join \"user_contacts\" on \"user_contacts\".\"id\" = \"registered_domains\".\"user_contact_registrar_id\" inner join \"users\" on \"users\".\"id\" = \"user_contacts\".\"user_id\" left join \"redemption_orders\" on \"redemption_orders\".\"domain_id\" = \"domains\".\"id\" and \"redemption_orders\".\"user_id\" = \"users\".\"id\" where \"pending_domain_deletions\".\"deleted_at\" is not null)","code":"42P01"}  
[2025-09-04 06:42:13] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-04 06:42:16] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation \"redemption_orders\" does not exist
LINE 1: ...users\".\"id\" = \"user_contacts\".\"user_id\" left join \"redemptio...
                                                             ^ (Connection: client, SQL: select count(*) as aggregate from \"pending_domain_deletions\" inner join \"registered_domains\" on \"registered_domains\".\"id\" = \"pending_domain_deletions\".\"registered_domain_id\" inner join \"domains\" on \"domains\".\"id\" = \"registered_domains\".\"domain_id\" inner join \"user_contacts\" on \"user_contacts\".\"id\" = \"registered_domains\".\"user_contact_registrar_id\" inner join \"users\" on \"users\".\"id\" = \"user_contacts\".\"user_id\" left join \"redemption_orders\" on \"redemption_orders\".\"domain_id\" = \"domains\".\"id\" and \"redemption_orders\".\"user_id\" = \"users\".\"id\" where \"pending_domain_deletions\".\"deleted_at\" is not null)","code":"42P01"}  
[2025-09-04 06:43:27] local.INFO: Successfully removed clientDeleteProhibited status for domain: ahalawsaw.org  
[2025-09-04 06:43:29] local.INFO: Successfully deleted domain from EPP and datastore: ahalawsaw.org  
[2025-09-04 06:43:55] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-04 06:44:22] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation \"redemption_orders\" does not exist
LINE 1: ...users\".\"id\" = \"user_contacts\".\"user_id\" left join \"redemptio...
                                                             ^ (Connection: client, SQL: select count(*) as aggregate from \"pending_domain_deletions\" inner join \"registered_domains\" on \"registered_domains\".\"id\" = \"pending_domain_deletions\".\"registered_domain_id\" inner join \"domains\" on \"domains\".\"id\" = \"registered_domains\".\"domain_id\" inner join \"user_contacts\" on \"user_contacts\".\"id\" = \"registered_domains\".\"user_contact_registrar_id\" inner join \"users\" on \"users\".\"id\" = \"user_contacts\".\"user_id\" left join \"redemption_orders\" on \"redemption_orders\".\"domain_id\" = \"domains\".\"id\" and \"redemption_orders\".\"user_id\" = \"users\".\"id\" where \"pending_domain_deletions\".\"deleted_at\" is not null)","code":"42P01"}  
[2025-09-04 07:41:13] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-09-05 01:34:18] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\View\\ViewException","message":"Vite manifest not found at: C:\\1xampp\\htdocs\\sd-admin\\public\\build\/manifest.json (View: C:\\1xampp\\htdocs\\sd-admin\\resources\\views\\app.blade.php)","code":0}  
[2025-09-05 01:35:48] local.INFO: user login from 127.0.0.1  
[2025-09-05 01:36:27] local.ERROR: {"query":[],"parameter":{"domainId":99,"createdDate":"2025-08-19 07:35:38","support_note":"Domain deletion request rejected."},"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[42P01]: Undefined table: 7 ERROR:  relation \"domain_reject_request_jobs\" does not exist
LINE 1: insert into \"domain_reject_request_jobs\" (\"queue\", \"attempts...
                    ^ (Connection: pgsql, SQL: insert into \"domain_reject_request_jobs\" (\"queue\", \"attempts\", \"reserved_at\", \"available_at\", \"created_at\", \"payload\") values (VERISIGN-REJECT, 0, ?, 1757036187, 1757036187, {\"uuid\":\"9455382a-480e-4af6-a89f-93182f662aa5\",\"displayName\":\"App\\\\Modules\\\\RequestDelete\\\\Jobs\\\\DomainRejectRequestJob\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":true,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"App\\\\Modules\\\\RequestDelete\\\\Jobs\\\\DomainRejectRequestJob\",\"command\":\"O:53:\\\"App\\\\Modules\\\\RequestDelete\\\\Jobs\\\\DomainRejectRequestJob\\\":3:{s:61:\\\"\\u0000App\\\\Modules\\\\RequestDelete\\\\Jobs\\\\DomainRejectRequestJob\\u0000params\\\";a:10:{s:8:\\\"domainId\\\";s:2:\\\"99\\\";s:10:\\\"domainName\\\";s:13:\\\"cdforfree.com\\\";s:6:\\\"userId\\\";s:1:\\\"7\\\";s:9:\\\"userEmail\\\";s:19:\\\"<EMAIL>\\\";s:6:\\\"reason\\\";s:17:\\\"Portfolio Cleanup\\\";s:11:\\\"createdDate\\\";s:19:\\\"2025-09-05 01:33:40\\\";s:11:\\\"supportNote\\\";s:33:\\\"Domain deletion request rejected.\\\";s:7:\\\"adminId\\\";i:1;s:9:\\\"adminName\\\";s:7:\\\"admin 1\\\";s:10:\\\"adminEmail\\\";s:5:\\\"a@a.a\\\";}s:10:\\\"connection\\\";s:26:\\\"domain_reject_request_jobs\\\";s:5:\\\"queue\\\";s:15:\\\"VERISIGN-REJECT\\\";}\"},\"illuminate:log:context\":{\"data\":[],\"hidden\":{\"laravel_unique_job_cache_store\":\"s:4:\\\"file\\\";\",\"laravel_unique_job_key\":\"s:89:\\\"laravel_unique_job:App\\\\Modules\\\\RequestDelete\\\\Jobs\\\\DomainRejectRequestJob:99_cdforfree.com\\\";\"}}}) returning \"id\")","code":"42P01"}  
[2025-09-05 01:40:11] local.INFO: Domain History: Domain deletion request rejected by admin 1 (a@a.a)  
