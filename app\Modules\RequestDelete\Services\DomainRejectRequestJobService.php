<?php

namespace App\Modules\RequestDelete\Services;

use App\Modules\Epp\Services\EppDomainService;
use App\Modules\Notification\Services\NotificationService;
use App\Util\Constant\UserDomainStatus;
use App\Util\Constant\DomainStatus;
use Illuminate\Support\Facades\DB;
use App\Modules\CustomLogger\Services\AuthLogger;
use Carbon\Carbon;

class DomainRejectRequestJobService
{
    public static function instance(): DomainRejectRequestJobService
    {
        return new self;
    }

    public function reactivateDomain(array $domain): void
    {
        $datastoreInfoResponse = EppDomainService::instance()->callDatastoreDomainInfo($domain['domainName']);

        if (!isset($datastoreInfoResponse['data']) || is_null($datastoreInfoResponse['data'])) {
            app(AuthLogger::class)->error("Domain {$domain['domainName']} not found in datastore. Response: " . json_encode($datastoreInfoResponse));
            throw new \Exception("Domain {$domain['domainName']} not found in datastore");
        }

        $datastoreInfo = $datastoreInfoResponse['data'];

        if (!is_array($datastoreInfo) || !isset($datastoreInfo['expiry'])) {
            app(AuthLogger::class)->error("Invalid datastore info structure for domain {$domain['domainName']}. Info: " . json_encode($datastoreInfo));
            throw new \Exception("Invalid datastore info structure for domain {$domain['domainName']}");
        }

        $expiryDate = Carbon::parse($datastoreInfo['expiry']);
        $now = Carbon::now();
        $isExpired = $expiryDate->isPast();
        $status = $isExpired ? DomainStatus::EXPIRED : DomainStatus::ACTIVE;

        $this->updateDomainStatus($domain, $status);
    }

    private function updateDomainStatus(array $domain, string $status): void
    {
        $timestamp = now();

        DB::client()->table('domains')
            ->where('id', $domain['domainId'])
            ->update([
                'status' => $status,
                'updated_at' => $timestamp,
            ]);

    }
}
