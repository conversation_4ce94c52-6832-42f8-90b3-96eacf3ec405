<?php

namespace App\Modules\RequestDelete\Services;

use App\Modules\Epp\Services\EppDomainService;
use App\Modules\Notification\Services\NotificationService;
use App\Util\Constant\UserDomainStatus;
use App\Util\Constant\DomainStatus;
use Illuminate\Support\Facades\DB;
use App\Modules\CustomLogger\Services\AuthLogger;
use Carbon\Carbon;

class DomainRejectRequestJobService
{
    public static function instance(): DomainRejectRequestJobService
    {
        return new self;
    }

    public function reactivateDomain(array $domain): void
    {
        $datastoreInfoResponse = EppDomainService::instance()->callDatastoreDomainInfo($domain['domainName']);

        if (!isset($datastoreInfoResponse['data']) || is_null($datastoreInfoResponse['data'])) {
            app(AuthLogger::class)->error("Domain {$domain['domainName']} not found in datastore. Response: " . json_encode($datastoreInfoResponse));
            throw new \Exception("Domain {$domain['domainName']} not found in datastore");
        }

        $datastoreInfo = $datastoreInfoResponse['data'];

        if (!is_array($datastoreInfo) || !isset($datastoreInfo['expiry'])) {
            app(AuthLogger::class)->error("Invalid datastore info structure for domain {$domain['domainName']}. Info: " . json_encode($datastoreInfo));
            throw new \Exception("Invalid datastore info structure for domain {$domain['domainName']}");
        }

        $expiryDate = Carbon::parse($datastoreInfo['expiry']);
        $now = Carbon::now();
        $isExpired = $expiryDate->isPast();
        $status = $isExpired ? DomainStatus::EXPIRED : DomainStatus::ACTIVE;

        $this->updateDomainStatus($domain, $status);
        $this->updateDomainCancellationRequestToRejected($domain);
    }

    private function updateDomainStatus(array $domain, string $status): void
    {
        $timestamp = now();

        DB::client()->table('domains')
            ->where('id', $domain['domainId'])
            ->update([
                'status' => $status,
                'updated_at' => $timestamp,
            ]);

        $registeredDomainStatus = $status === DomainStatus::EXPIRED ? UserDomainStatus::EXPIRED : UserDomainStatus::OWNED;
        
        DB::client()->table('registered_domains')
            ->where('domain_id', $domain['domainId'])
            ->update([
                'status' => $registeredDomainStatus,
                'updated_at' => $timestamp,
            ]);

        app(AuthLogger::class)->info("Updated domain {$domain['domainName']} status to {$status} in domains table and {$registeredDomainStatus} in registered_domains table");
    }

    private function updateDomainCancellationRequestToRejected(array $domain): void
    {
        $adminId = $domain['adminId'] ?? 1;
        $adminName = $domain['adminName'] ?? 'System';
        $adminEmail = $domain['adminEmail'] ?? '<EMAIL>';
        $adminFullName = "{$adminName} ({$adminEmail})";
        $supportNote = $domain['supportNote'] ?? "{$adminFullName} rejected domain deletion request for: {$domain['domainName']}";

        $exists = DB::client()
            ->table('domain_cancellation_requests')
            ->where('domain_id', $domain['domainId'])
            ->exists();

        if ($exists) {
            DB::client()->table('domain_cancellation_requests')
                ->where('domain_id', $domain['domainId'])
                ->update([
                    'status' => 'REJECTED',
                    'support_agent_id' => $adminId,
                    'support_agent_name' => $adminFullName,
                    'feedback_date' => now(),
                    'support_note' => $supportNote,
                    'updated_at' => now(),
                ]);

            app(AuthLogger::class)->info("Updated domain cancellation request for {$domain['domainName']} to REJECTED status");
        } else {
            app(AuthLogger::class)->error("No domain cancellation request found for domain {$domain['domainName']} (ID: {$domain['domainId']})");
        }
    }
}
